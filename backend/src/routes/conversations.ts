// Conversation History Routes for MiCA Therapy Application
// REST API endpoints for retrieving conversation data, messages, and analysis

import express from 'express';
import { databaseService } from '../services/database/index.js';
import {
  ConversationQueryOptions,
  MessageQueryOptions,
  AnalysisQueryOptions
} from '../types/index.js';

const router = express.Router();

/**
 * GET /api/conversations
 * Get all conversations with optional filtering and pagination
 */
router.get('/', async (req, res) => {
  try {
    const {
      status,
      patientPersonaId,
      therapistMode,
      sessionType,
      startDate,
      endDate,
      limit = 50,
      offset = 0,
      orderBy = 'created_at',
      orderDirection = 'desc'
    } = req.query;

    const options: ConversationQueryOptions = {
      status: status as any,
      patientPersonaId: patientPersonaId as string,
      therapistMode: therapistMode as any,
      sessionType: sessionType as string,
      limit: Number(limit),
      offset: Number(offset),
      orderBy: orderBy as any,
      orderDirection: orderDirection as any
    };

    if (startDate && endDate) {
      options.dateRange = {
        start: startDate as string,
        end: endDate as string
      };
    }

    const conversations = await databaseService.queryConversations(options);

    res.json({
      success: true,
      data: conversations,
      pagination: {
        limit: Number(limit),
        offset: Number(offset),
        total: conversations.length
      }
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId
 * Get a specific conversation by ID
 */
router.get('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { includeMessages = 'true', includeAnalysis = 'false', includeTherapeuticApproaches = 'false' } = req.query;

    const conversation = await databaseService.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
        message: `No conversation found with ID: ${conversationId}`
      });
    }

    const result: any = { conversation };

    // Include additional data based on query parameters
    if (includeMessages === 'true') {
      result.messages = await databaseService.getConversationMessages(conversationId);
    }

    if (includeAnalysis === 'true') {
      result.analysis = await databaseService.getConversationAnalysis(conversationId);
    }

    if (includeTherapeuticApproaches === 'true') {
      result.therapeuticApproaches = await databaseService.getConversationTherapeuticApproaches(conversationId);
    }

    // Include session analytics if available
    result.sessionAnalytics = await databaseService.getSessionAnalytics(conversationId);

    // Include multi-therapist data if applicable
    if (conversation.therapist_mode === 'multi-therapist') {
      result.multiTherapistSessions = await databaseService.getMultiTherapistSessions(conversationId);
      result.performanceComparison = await databaseService.getPerformanceComparison(conversationId);
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/complete
 * Get complete conversation data including all related information
 */
router.get('/:conversationId/complete', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const completeData = await databaseService.getCompleteConversationData(conversationId);

    if (!completeData.conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
        message: `No conversation found with ID: ${conversationId}`
      });
    }

    res.json({
      success: true,
      data: completeData
    });
  } catch (error) {
    console.error('Error fetching complete conversation data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch complete conversation data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/messages
 * Get messages for a specific conversation with filtering options
 */
router.get('/:conversationId/messages', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const {
      sender,
      messageType,
      startDate,
      endDate,
      includeAnalysis = 'false',
      includeTherapeuticApproaches = 'false',
      limit = 100,
      offset = 0
    } = req.query;

    const options: MessageQueryOptions = {
      sender: sender as any,
      messageType: messageType as any,
      includeAnalysis: includeAnalysis === 'true',
      includeTherapeuticApproaches: includeTherapeuticApproaches === 'true',
      limit: Number(limit),
      offset: Number(offset)
    };

    if (startDate && endDate) {
      options.dateRange = {
        start: startDate as string,
        end: endDate as string
      };
    }

    const messages = await databaseService.getConversationMessages(conversationId, options);

    // Include analysis data if requested
    const result: any = { messages };

    if (includeAnalysis === 'true') {
      const analysisPromises = messages.map(msg => 
        databaseService.getMessageAnalysis(msg.id)
      );
      const analysisResults = await Promise.all(analysisPromises);
      
      result.messageAnalysis = {};
      messages.forEach((msg, index) => {
        result.messageAnalysis[msg.id] = analysisResults[index];
      });
    }

    if (includeTherapeuticApproaches === 'true') {
      result.therapeuticApproaches = await databaseService.getConversationTherapeuticApproaches(conversationId);
    }

    res.json({
      success: true,
      data: result,
      pagination: {
        limit: Number(limit),
        offset: Number(offset),
        total: messages.length
      }
    });
  } catch (error) {
    console.error('Error fetching conversation messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation messages',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/analysis
 * Get AI analysis data for a specific conversation
 */
router.get('/:conversationId/analysis', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const {
      analysisType,
      startDate,
      endDate,
      limit = 100,
      offset = 0
    } = req.query;

    const options: AnalysisQueryOptions = {
      analysisType: analysisType as any,
      limit: Number(limit),
      offset: Number(offset)
    };

    if (startDate && endDate) {
      options.dateRange = {
        start: startDate as string,
        end: endDate as string
      };
    }

    const analysis = await databaseService.getConversationAnalysis(conversationId, options);

    res.json({
      success: true,
      data: analysis,
      pagination: {
        limit: Number(limit),
        offset: Number(offset),
        total: analysis.length
      }
    });
  } catch (error) {
    console.error('Error fetching conversation analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation analysis',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/analytics
 * Get session analytics for a specific conversation
 */
router.get('/:conversationId/analytics', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const analytics = await databaseService.getSessionAnalytics(conversationId);

    if (!analytics) {
      return res.status(404).json({
        success: false,
        error: 'Session analytics not found',
        message: `No session analytics found for conversation: ${conversationId}`
      });
    }

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching session analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch session analytics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/multi-therapist
 * Get multi-therapist session data for a specific conversation
 */
router.get('/:conversationId/multi-therapist', async (req, res) => {
  try {
    const { conversationId } = req.params;

    // Verify this is a multi-therapist conversation
    const conversation = await databaseService.getConversation(conversationId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
        message: `No conversation found with ID: ${conversationId}`
      });
    }

    if (conversation.therapist_mode !== 'multi-therapist') {
      return res.status(400).json({
        success: false,
        error: 'Not a multi-therapist conversation',
        message: 'This endpoint is only available for multi-therapist conversations'
      });
    }

    const multiTherapistSessions = await databaseService.getMultiTherapistSessions(conversationId);
    const performanceComparison = await databaseService.getPerformanceComparison(conversationId);

    res.json({
      success: true,
      data: {
        sessions: multiTherapistSessions,
        performanceComparison
      }
    });
  } catch (error) {
    console.error('Error fetching multi-therapist data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch multi-therapist data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/conversations/:conversationId/multi-therapist/:personaType
 * Get specific persona session data
 */
router.get('/:conversationId/multi-therapist/:personaType', async (req, res) => {
  try {
    const { conversationId, personaType } = req.params;

    // Validate persona type
    const validPersonaTypes = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];
    if (!validPersonaTypes.includes(personaType)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid persona type',
        message: `Persona type must be one of: ${validPersonaTypes.join(', ')}`
      });
    }

    const personaSession = await databaseService.getPersonaSession(conversationId, personaType as any);

    if (!personaSession) {
      return res.status(404).json({
        success: false,
        error: 'Persona session not found',
        message: `No session found for persona ${personaType} in conversation ${conversationId}`
      });
    }

    res.json({
      success: true,
      data: personaSession
    });
  } catch (error) {
    console.error('Error fetching persona session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch persona session',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/conversations/:conversationId
 * Delete a conversation and all related data
 */
router.delete('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    // Verify conversation exists
    const conversation = await databaseService.getConversation(conversationId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
        message: `No conversation found with ID: ${conversationId}`
      });
    }

    // Note: Due to CASCADE DELETE constraints in the database schema,
    // deleting the conversation will automatically delete all related data
    // (messages, analysis, therapeutic approaches, session analytics, etc.)

    // For now, we'll just mark the conversation as deleted rather than actually deleting it
    // This preserves data for research purposes
    await databaseService.updateConversation(conversationId, {
      status: 'completed' // or we could add a 'deleted' status
    });

    res.json({
      success: true,
      message: `Conversation ${conversationId} has been marked as completed`
    });
  } catch (error) {
    console.error('Error deleting conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete conversation',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
