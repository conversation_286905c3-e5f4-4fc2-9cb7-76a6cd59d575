// Therapeutic Approaches Framework for MiCA Therapy Simulation
// Comprehensive definitions for CBT and Motivational Interviewing approaches

export interface TherapeuticTechnique {
  id: string;
  name: string;
  description: string;
  prompt: string;
  selectionCriteria: {
    patientStates: string[];
    situationalFactors: string[];
    contraindications?: string[];
  };
  expectedOutcomes: string[];
}

export interface TherapeuticApproach {
  id: string;
  name: string;
  description: string;
  philosophy: string;
  readinessThreshold: {
    min: number;
    max: number;
  };
  techniques: TherapeuticTechnique[];
  selectionCriteria: {
    patientReadiness: string;
    appropriateFor: string[];
    goals: string[];
  };
}

export interface ReadinessAssessment {
  score: number; // 1-10 scale
  factors: {
    sentiment: {
      value: 'positive' | 'negative' | 'neutral';
      weight: number;
      contribution: number;
    };
    sentimentIntensity: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivation: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    motivationType: {
      value: 'intrinsic' | 'extrinsic' | 'mixed';
      weight: number;
      contribution: number;
    };
    engagement: {
      value: 'low' | 'medium' | 'high';
      weight: number;
      contribution: number;
    };
    engagementPatterns: {
      value: string[];
      weight: number;
      contribution: number;
    };
  };
  reasoning: string;
  recommendedApproach: 'CBT' | 'MI';
  indicators: {
    positive: string[];
    negative: string[];
  };
}

// Motivational Interviewing Techniques
export const motivationalInterviewingTechniques: TherapeuticTechnique[] = [
  {
    id: 'mi-open-questions',
    name: 'Open-Ended Questions',
    description: 'Questions that encourage elaboration and exploration of thoughts and feelings',
    prompt: 'Ask open-ended questions that help the patient explore their thoughts, feelings, and motivations. Avoid yes/no questions. Focus on understanding their perspective and encouraging self-reflection.',
    selectionCriteria: {
      patientStates: ['guarded', 'withdrawn', 'ambivalent', 'exploring'],
      situationalFactors: ['initial sessions', 'resistance to change', 'need for exploration']
    },
    expectedOutcomes: ['increased self-reflection', 'deeper exploration', 'enhanced engagement']
  },
  {
    id: 'mi-reflective-listening',
    name: 'Reflective Listening',
    description: 'Reflecting back what the patient has said to demonstrate understanding and encourage further exploration',
    prompt: 'Reflect back what the patient has shared, demonstrating deep understanding. Use phrases like "It sounds like..." or "What I hear you saying is..." to show you are listening and to encourage them to continue.',
    selectionCriteria: {
      patientStates: ['emotional', 'confused', 'conflicted', 'sharing'],
      situationalFactors: ['building rapport', 'validating emotions', 'encouraging openness']
    },
    expectedOutcomes: ['increased trust', 'feeling heard', 'continued sharing']
  },
  {
    id: 'mi-affirmations',
    name: 'Affirmations',
    description: 'Recognizing and affirming the patient\'s strengths, efforts, and positive qualities',
    prompt: 'Acknowledge and affirm the patient\'s strengths, efforts, or positive qualities. Focus on their resilience, courage in seeking help, or any positive steps they\'ve taken.',
    selectionCriteria: {
      patientStates: ['low self-esteem', 'discouraged', 'self-critical', 'making effort'],
      situationalFactors: ['building confidence', 'recognizing progress', 'encouraging hope']
    },
    expectedOutcomes: ['increased self-efficacy', 'improved mood', 'enhanced motivation']
  },
  {
    id: 'mi-summarizing',
    name: 'Summarizing',
    description: 'Pulling together key themes and reflecting them back to reinforce important points',
    prompt: 'Summarize the key themes and important points from what the patient has shared. This helps consolidate their thoughts and shows you understand their situation comprehensively.',
    selectionCriteria: {
      patientStates: ['scattered thoughts', 'multiple concerns', 'processing'],
      situationalFactors: ['end of session', 'clarifying priorities', 'consolidating insights']
    },
    expectedOutcomes: ['clarity of thought', 'prioritized concerns', 'sense of progress']
  },
  {
    id: 'mi-change-talk',
    name: 'Eliciting Change Talk',
    description: 'Encouraging the patient to express their own motivations and reasons for change',
    prompt: 'Ask questions that help the patient express their own reasons for change, their hopes, and their concerns about their current situation. Focus on eliciting their internal motivation.',
    selectionCriteria: {
      patientStates: ['ambivalent', 'considering change', 'motivated'],
      situationalFactors: ['exploring motivation', 'building commitment', 'preparing for action']
    },
    expectedOutcomes: ['increased intrinsic motivation', 'commitment to change', 'self-advocacy']
  },
  {
    id: 'mi-rolling-resistance',
    name: 'Rolling with Resistance',
    description: 'Avoiding confrontation and instead exploring the patient\'s perspective when they show resistance',
    prompt: 'When the patient shows resistance or defensiveness, avoid arguing. Instead, explore their perspective with curiosity and respect. Acknowledge their autonomy and right to make their own decisions.',
    selectionCriteria: {
      patientStates: ['resistant', 'defensive', 'argumentative', 'ambivalent'],
      situationalFactors: ['confronting resistance', 'maintaining rapport', 'respecting autonomy']
    },
    expectedOutcomes: ['reduced defensiveness', 'maintained rapport', 'continued engagement']
  },
  {
    id: 'mi-developing-discrepancy',
    name: 'Developing Discrepancy',
    description: 'Helping patients recognize the gap between their current behavior and their values or goals',
    prompt: 'Help the patient explore the difference between where they are now and where they want to be. Ask about their values and goals, then gently highlight how their current situation may not align with these.',
    selectionCriteria: {
      patientStates: ['ambivalent', 'considering change', 'conflicted'],
      situationalFactors: ['values exploration', 'goal setting', 'motivation building']
    },
    expectedOutcomes: ['increased awareness', 'enhanced motivation', 'clarity of goals']
  },
  {
    id: 'mi-expressing-empathy',
    name: 'Expressing Empathy',
    description: 'Demonstrating understanding and acceptance of the patient\'s experience',
    prompt: 'Show genuine understanding and acceptance of the patient\'s feelings and experiences. Use empathetic responses that validate their emotions without judgment.',
    selectionCriteria: {
      patientStates: ['emotional', 'vulnerable', 'sharing', 'distressed'],
      situationalFactors: ['building rapport', 'emotional support', 'trust building']
    },
    expectedOutcomes: ['increased trust', 'emotional validation', 'enhanced rapport']
  },
  {
    id: 'mi-supporting-self-efficacy',
    name: 'Supporting Self-Efficacy',
    description: 'Reinforcing the patient\'s belief in their ability to change and succeed',
    prompt: 'Highlight the patient\'s strengths, past successes, and capabilities. Express confidence in their ability to make positive changes and overcome challenges.',
    selectionCriteria: {
      patientStates: ['discouraged', 'low confidence', 'self-doubt', 'ready for change'],
      situationalFactors: ['building confidence', 'encouraging action', 'reinforcing strengths']
    },
    expectedOutcomes: ['increased confidence', 'enhanced self-belief', 'motivation for action']
  }
];

// Cognitive Behavioral Therapy Techniques
export const cbtTechniques: TherapeuticTechnique[] = [
  {
    id: 'cbt-thought-challenging',
    name: 'Thought Challenging',
    description: 'Helping patients identify and examine the validity of negative or unhelpful thoughts',
    prompt: 'Help the patient identify specific negative thoughts and examine the evidence for and against them. Ask questions like "What evidence supports this thought?" and "Are there alternative ways to view this situation?"',
    selectionCriteria: {
      patientStates: ['negative thinking', 'catastrophizing', 'self-critical', 'ready for change'],
      situationalFactors: ['cognitive distortions present', 'patient ready for challenge', 'specific thoughts identified']
    },
    expectedOutcomes: ['balanced thinking', 'reduced negative thoughts', 'improved mood']
  },
  {
    id: 'cbt-behavioral-activation',
    name: 'Behavioral Activation',
    description: 'Encouraging engagement in meaningful and pleasurable activities to improve mood and functioning',
    prompt: 'Explore activities that the patient used to enjoy or might find meaningful. Help them plan specific, achievable steps to re-engage with these activities, starting small and building up.',
    selectionCriteria: {
      patientStates: ['depressed', 'withdrawn', 'inactive', 'motivated for change'],
      situationalFactors: ['behavioral patterns identified', 'ready for action', 'specific goals possible']
    },
    expectedOutcomes: ['increased activity', 'improved mood', 'sense of accomplishment']
  },
  {
    id: 'cbt-problem-solving',
    name: 'Problem-Solving',
    description: 'Systematic approach to identifying problems and developing practical solutions',
    prompt: 'Help the patient break down their problems into specific, manageable parts. Guide them through brainstorming solutions, evaluating options, and creating action plans.',
    selectionCriteria: {
      patientStates: ['overwhelmed', 'stuck', 'practical problems', 'ready for solutions'],
      situationalFactors: ['specific problems identified', 'patient ready for action', 'practical solutions possible']
    },
    expectedOutcomes: ['clearer problem definition', 'practical solutions', 'increased confidence']
  },
  {
    id: 'cbt-cognitive-restructuring',
    name: 'Cognitive Restructuring',
    description: 'Systematic process of identifying, challenging, and replacing unhelpful thought patterns',
    prompt: 'Guide the patient through identifying thought patterns, examining their accuracy and helpfulness, and developing more balanced, realistic alternatives.',
    selectionCriteria: {
      patientStates: ['distorted thinking', 'negative patterns', 'ready for change', 'insightful'],
      situationalFactors: ['patterns identified', 'patient engaged', 'ready for cognitive work']
    },
    expectedOutcomes: ['improved thought patterns', 'better emotional regulation', 'increased insight']
  },
  {
    id: 'cbt-exposure-planning',
    name: 'Exposure Planning',
    description: 'Gradual, systematic approach to facing feared situations or thoughts',
    prompt: 'Help the patient identify specific fears or avoidance behaviors and create a gradual plan to face them in a safe, controlled way. Start with less threatening situations and build up.',
    selectionCriteria: {
      patientStates: ['anxious', 'avoidant', 'ready for challenge', 'motivated'],
      situationalFactors: ['specific fears identified', 'patient committed', 'safe environment']
    },
    expectedOutcomes: ['reduced avoidance', 'decreased anxiety', 'increased confidence']
  },
  {
    id: 'cbt-homework-assignment',
    name: 'Homework Assignment',
    description: 'Specific tasks or exercises for the patient to practice between sessions',
    prompt: 'Collaborate with the patient to identify specific, achievable tasks they can work on between sessions. Make sure the assignments are clear, relevant, and manageable.',
    selectionCriteria: {
      patientStates: ['engaged', 'motivated', 'ready for practice', 'committed'],
      situationalFactors: ['specific skills to practice', 'patient agreement', 'clear goals']
    },
    expectedOutcomes: ['skill practice', 'continued progress', 'increased self-efficacy']
  },
  {
    id: 'cbt-thought-records',
    name: 'Thought Records',
    description: 'Systematic tracking and analysis of thoughts, feelings, and situations',
    prompt: 'Guide the patient in identifying and recording their automatic thoughts in specific situations, along with associated emotions and behaviors. Help them analyze patterns and develop more balanced thoughts.',
    selectionCriteria: {
      patientStates: ['negative thinking', 'ready for self-monitoring', 'insightful'],
      situationalFactors: ['thought patterns identified', 'patient willing to track', 'homework appropriate']
    },
    expectedOutcomes: ['increased awareness', 'thought pattern recognition', 'cognitive flexibility']
  },
  {
    id: 'cbt-activity-scheduling',
    name: 'Activity Scheduling',
    description: 'Planning and scheduling meaningful and pleasurable activities',
    prompt: 'Work with the patient to schedule specific activities that bring meaning, pleasure, or accomplishment. Start with small, achievable activities and gradually increase complexity.',
    selectionCriteria: {
      patientStates: ['depressed', 'inactive', 'withdrawn', 'motivated for change'],
      situationalFactors: ['behavioral activation needed', 'specific activities identified', 'scheduling possible']
    },
    expectedOutcomes: ['increased activity', 'improved mood', 'behavioral activation']
  },
  {
    id: 'cbt-grounding-techniques',
    name: 'Grounding Techniques',
    description: 'Techniques to help patients stay present and manage overwhelming emotions',
    prompt: 'Teach the patient grounding techniques such as 5-4-3-2-1 sensory method, deep breathing, or mindfulness exercises to help them stay present during distressing moments.',
    selectionCriteria: {
      patientStates: ['anxious', 'overwhelmed', 'dissociative', 'panic'],
      situationalFactors: ['acute distress', 'need for immediate coping', 'anxiety management']
    },
    expectedOutcomes: ['reduced anxiety', 'increased present-moment awareness', 'improved emotional regulation']
  },
  {
    id: 'cbt-cost-benefit-analysis',
    name: 'Cost-Benefit Analysis',
    description: 'Systematic evaluation of the pros and cons of thoughts, behaviors, or decisions',
    prompt: 'Help the patient systematically examine the advantages and disadvantages of their current thoughts or behaviors, and compare them with alternative approaches.',
    selectionCriteria: {
      patientStates: ['ambivalent', 'decision-making', 'ready for analysis', 'rational'],
      situationalFactors: ['specific decisions needed', 'patient analytical', 'clear alternatives exist']
    },
    expectedOutcomes: ['clearer decision-making', 'balanced perspective', 'informed choices']
  },
  {
    id: 'cbt-behavioral-experiments',
    name: 'Behavioral Experiments',
    description: 'Testing beliefs and assumptions through planned behavioral activities',
    prompt: 'Design specific experiments with the patient to test their beliefs or assumptions in real-world situations. Plan the experiment, predict outcomes, and review results together.',
    selectionCriteria: {
      patientStates: ['ready for challenge', 'motivated', 'specific beliefs identified', 'experimental mindset'],
      situationalFactors: ['testable beliefs', 'safe environment', 'patient commitment']
    },
    expectedOutcomes: ['belief testing', 'new experiences', 'cognitive flexibility']
  }
];

// Dialectical Behavior Therapy Techniques
export const dbtTechniques: TherapeuticTechnique[] = [
  {
    id: 'dbt-distress-tolerance',
    name: 'Distress Tolerance Skills',
    description: 'Teaching skills to survive crisis situations without making them worse through impulsive actions',
    prompt: 'Help the patient identify current distress level and guide them through appropriate distress tolerance skills such as TIPP, distraction techniques, self-soothing, or radical acceptance.',
    selectionCriteria: {
      patientStates: ['crisis', 'high distress', 'impulsive urges', 'emotional overwhelm'],
      situationalFactors: ['crisis situation', 'high emotional intensity', 'urge to act impulsively']
    },
    expectedOutcomes: ['crisis survival', 'reduced impulsivity', 'emotional stabilization']
  },
  {
    id: 'dbt-emotion-regulation',
    name: 'Emotion Regulation Skills',
    description: 'Teaching skills to understand, name, and change emotions',
    prompt: 'Guide the patient in identifying and labeling their emotions, understanding the function of emotions, and using skills like opposite action, PLEASE, or emotion surfing to regulate emotional responses.',
    selectionCriteria: {
      patientStates: ['emotional dysregulation', 'intense emotions', 'mood swings', 'emotional confusion'],
      situationalFactors: ['emotional triggers present', 'pattern of emotional instability', 'need for emotional skills']
    },
    expectedOutcomes: ['emotional awareness', 'emotional stability', 'improved emotional control']
  },
  {
    id: 'dbt-interpersonal-effectiveness',
    name: 'Interpersonal Effectiveness Skills',
    description: 'Teaching skills to ask for what you need, say no, and cope with interpersonal conflict',
    prompt: 'Help the patient practice DEAR MAN, GIVE, or FAST skills for interpersonal situations. Focus on balancing priorities in relationships while maintaining self-respect and effectiveness.',
    selectionCriteria: {
      patientStates: ['interpersonal conflict', 'relationship difficulties', 'boundary issues', 'communication problems'],
      situationalFactors: ['relationship challenges', 'need to set boundaries', 'interpersonal goals']
    },
    expectedOutcomes: ['improved relationships', 'better communication', 'healthy boundaries']
  },
  {
    id: 'dbt-mindfulness',
    name: 'Mindfulness Skills',
    description: 'Teaching core mindfulness skills to observe, describe, and participate in the present moment',
    prompt: 'Guide the patient in practicing mindfulness skills such as wise mind, observe/describe/participate, or one-mindfully. Focus on present moment awareness and non-judgmental stance.',
    selectionCriteria: {
      patientStates: ['dissociation', 'emotional mind', 'rumination', 'distraction'],
      situationalFactors: ['need for grounding', 'emotional dysregulation', 'lack of present moment awareness']
    },
    expectedOutcomes: ['present moment awareness', 'emotional balance', 'wise mind access']
  },
  {
    id: 'dbt-radical-acceptance',
    name: 'Radical Acceptance',
    description: 'Teaching acceptance of reality as it is, without approval or resignation',
    prompt: 'Help the patient understand and practice radical acceptance of difficult situations or emotions. Guide them through the process of accepting reality while still working toward change.',
    selectionCriteria: {
      patientStates: ['fighting reality', 'suffering from non-acceptance', 'stuck in anger or denial'],
      situationalFactors: ['unchangeable situation', 'need to reduce suffering', 'resistance to reality']
    },
    expectedOutcomes: ['reduced suffering', 'acceptance of reality', 'freedom to focus on changeable aspects']
  }
];

// Psychodynamic Therapy Techniques
export const psychodynamicTechniques: TherapeuticTechnique[] = [
  {
    id: 'psychodynamic-free-association',
    name: 'Free Association',
    description: 'Encouraging the patient to speak freely about whatever comes to mind',
    prompt: 'Invite the patient to share whatever thoughts, feelings, or memories come to mind without censoring. Pay attention to themes, patterns, and what might be avoided or emphasized.',
    selectionCriteria: {
      patientStates: ['defensive', 'intellectualizing', 'need for exploration', 'unconscious material emerging'],
      situationalFactors: ['therapeutic alliance established', 'patient ready for exploration', 'need to access unconscious']
    },
    expectedOutcomes: ['unconscious material emergence', 'increased self-awareness', 'pattern recognition']
  },
  {
    id: 'psychodynamic-interpretation',
    name: 'Interpretation',
    description: 'Offering insights about unconscious patterns, defenses, or dynamics',
    prompt: 'Carefully offer interpretations about patterns you observe in the patient\'s material. Connect current experiences to past relationships or unconscious conflicts. Time interpretations when the patient is ready to hear them.',
    selectionCriteria: {
      patientStates: ['ready for insight', 'pattern recognition emerging', 'defensive patterns clear'],
      situationalFactors: ['strong therapeutic alliance', 'repeated patterns observed', 'patient showing curiosity']
    },
    expectedOutcomes: ['increased insight', 'pattern awareness', 'unconscious made conscious']
  },
  {
    id: 'psychodynamic-transference-analysis',
    name: 'Transference Analysis',
    description: 'Exploring how past relationships are recreated in the therapeutic relationship',
    prompt: 'Gently explore how the patient might be experiencing you or the therapeutic relationship in ways that reflect past important relationships. Help them recognize these patterns.',
    selectionCriteria: {
      patientStates: ['transference reactions evident', 'relationship patterns emerging', 'therapeutic alliance strong'],
      situationalFactors: ['clear transference patterns', 'patient ready for exploration', 'therapeutic relationship established']
    },
    expectedOutcomes: ['transference awareness', 'relationship pattern insight', 'corrective emotional experience']
  },
  {
    id: 'psychodynamic-defense-analysis',
    name: 'Defense Analysis',
    description: 'Identifying and exploring psychological defense mechanisms',
    prompt: 'Help the patient recognize their characteristic defense mechanisms and understand how these defenses both protect and limit them. Explore the origins and current function of these defenses.',
    selectionCriteria: {
      patientStates: ['defensive patterns evident', 'avoidance behaviors', 'emotional numbing', 'intellectualizing'],
      situationalFactors: ['clear defense patterns', 'patient showing some awareness', 'therapeutic alliance present']
    },
    expectedOutcomes: ['defense awareness', 'increased emotional access', 'adaptive coping development']
  },
  {
    id: 'psychodynamic-dream-analysis',
    name: 'Dream Analysis',
    description: 'Exploring dreams as a window into unconscious material',
    prompt: 'When the patient shares dreams, explore them as potential expressions of unconscious wishes, fears, or conflicts. Look for symbolic meaning and connections to current life situations.',
    selectionCriteria: {
      patientStates: ['sharing dreams', 'curious about unconscious', 'symbolic thinking present'],
      situationalFactors: ['dream material available', 'patient interested in exploration', 'therapeutic alliance strong']
    },
    expectedOutcomes: ['unconscious insight', 'symbolic understanding', 'deeper self-awareness']
  }
];

// Therapeutic Approaches Definitions
export const therapeuticApproaches: TherapeuticApproach[] = [
  {
    id: 'motivational-interviewing',
    name: 'Motivational Interviewing',
    description: 'A collaborative, goal-oriented style of communication designed to strengthen personal motivation and commitment to change',
    philosophy: 'Respects patient autonomy and uses their own motivations to facilitate change. Focuses on exploring and resolving ambivalence.',
    readinessThreshold: {
      min: 1,
      max: 7
    },
    techniques: motivationalInterviewingTechniques,
    selectionCriteria: {
      patientReadiness: 'Low to moderate readiness for change (scores 1-7)',
      appropriateFor: ['ambivalent patients', 'resistant to change', 'early stages of therapy', 'building motivation'],
      goals: ['increase motivation', 'build rapport', 'explore ambivalence', 'enhance engagement']
    }
  },
  {
    id: 'cognitive-behavioral-therapy',
    name: 'Cognitive Behavioral Therapy',
    description: 'A structured, goal-oriented approach that focuses on identifying and changing unhelpful thought patterns and behaviors',
    philosophy: 'Problems are maintained by unhelpful thinking patterns and behaviors. Change occurs through identifying and modifying these patterns.',
    readinessThreshold: {
      min: 8,
      max: 10
    },
    techniques: cbtTechniques,
    selectionCriteria: {
      patientReadiness: 'High readiness for change (scores 8-10)',
      appropriateFor: ['motivated patients', 'ready for active work', 'specific problems identified', 'goal-oriented'],
      goals: ['change thought patterns', 'modify behaviors', 'develop coping skills', 'solve specific problems']
    }
  },
  {
    id: 'dialectical-behavior-therapy',
    name: 'Dialectical Behavior Therapy',
    description: 'A comprehensive treatment that teaches skills for emotional regulation, distress tolerance, interpersonal effectiveness, and mindfulness',
    philosophy: 'Balances acceptance and change strategies. Focuses on building skills to manage intense emotions and improve relationships while accepting current reality.',
    readinessThreshold: {
      min: 1,
      max: 10
    },
    techniques: dbtTechniques,
    selectionCriteria: {
      patientReadiness: 'Any readiness level (scores 1-10)',
      appropriateFor: ['emotional dysregulation', 'interpersonal difficulties', 'crisis situations', 'self-harm behaviors', 'intense emotions'],
      goals: ['emotional regulation', 'distress tolerance', 'interpersonal effectiveness', 'mindfulness skills', 'crisis survival']
    }
  },
  {
    id: 'psychodynamic-therapy',
    name: 'Psychodynamic Therapy',
    description: 'An insight-oriented approach that explores unconscious patterns, early relationships, and their impact on current functioning',
    philosophy: 'Current difficulties stem from unconscious conflicts and patterns rooted in early relationships. Insight and working through lead to lasting change.',
    readinessThreshold: {
      min: 1,
      max: 10
    },
    techniques: psychodynamicTechniques,
    selectionCriteria: {
      patientReadiness: 'Any readiness level (scores 1-10)',
      appropriateFor: ['relationship patterns', 'recurring life themes', 'identity issues', 'complex trauma', 'personality concerns'],
      goals: ['increase self-awareness', 'understand unconscious patterns', 'improve relationships', 'resolve internal conflicts', 'develop authentic self']
    }
  }
];

// Readiness Score Calculation Weights
export const readinessWeights = {
  sentiment: {
    positive: 3,
    neutral: 2,
    negative: 1,
    weight: 0.2
  },
  sentimentIntensity: {
    high: 3,
    medium: 2,
    low: 1,
    weight: 0.1
  },
  motivation: {
    high: 3,
    medium: 2,
    low: 1,
    weight: 0.25
  },
  motivationType: {
    intrinsic: 3,
    mixed: 2,
    extrinsic: 1,
    weight: 0.15
  },
  engagement: {
    high: 3,
    medium: 2,
    low: 1,
    weight: 0.2
  },
  engagementPatterns: {
    positive: ['active participation', 'openness', 'curiosity'],
    negative: ['resistance', 'withdrawal', 'defensiveness', 'avoidance'],
    weight: 0.1
  }
};
