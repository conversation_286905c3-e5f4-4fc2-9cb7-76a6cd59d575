// Core types for MiCA Therapy Simulation

export interface Message {
  id: string;
  conversationId: string;
  sender: 'therapist' | 'patient';
  content: string;
  timestamp: string;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  sentiment?: 'positive' | 'negative' | 'neutral';
  motivationLevel?: 'low' | 'medium' | 'high';
  engagementLevel?: 'low' | 'medium' | 'high';
  confidence?: number;
}

export interface AgentThought {
  id: string;
  agentType: 'therapist' | 'patient';
  content: string;
  timestamp: string;
  messageId?: string;
  type: 'analysis' | 'planning' | 'reflection' | 'decision';
}

// CBT Evaluation Types
export interface CBTEvaluationDimension {
  name: string;
  score: number; // 0, 2, 4, or 6
  criteria: string;
  rationale?: string;
}

export interface CBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  dimensions: {
    cbtValidity: CBTEvaluationDimension;
    cbtAppropriateness: CBTEvaluationDimension;
    cbtAccuracy: CBTEvaluationDimension;
    esAppropriateness: CBTEvaluationDimension;
    stability: CBTEvaluationDimension;
  };
  overallScore: number; // Average of all dimension scores
  overallAssessment: 'poor' | 'fair' | 'good' | 'excellent';
  conversationSummary: string;
  recommendations: string[];
  metadata: {
    totalMessages: number;
    sessionDuration: number; // in minutes
    evaluationModel: string;
    evaluationVersion: string;
  };
}

export interface Conversation {
  id: string;
  status: 'active' | 'completed' | 'paused';
  startTime: string;
  endTime?: string;
  config: ConversationConfig;
  messages: Message[];
  thoughts: AgentThought[];
  analytics?: ConversationAnalytics;
}

export interface ConversationConfig {
  maxTurns: number;
  therapistPersona?: string;
  patientPersona?: string;
  scenario?: string;
  autoAdvance?: boolean;
  responseDelay?: number;
}

export interface ConversationAnalytics {
  totalTurns: number;
  averageResponseTime: number;
  sentimentProgression: Array<{
    turn: number;
    sentiment: string;
    confidence: number;
  }>;
  engagementTrend: Array<{
    turn: number;
    level: string;
  }>;
}

// Multi-Therapist Types
export type TherapistPersonaType = 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive';

export interface TherapistPersonaMessage extends Message {
  personaType: TherapistPersonaType;
  personaName: string;
  strategyState?: {
    currentApproach: 'CBT' | 'MI';
    hasSwitch: boolean;
    switchReason?: string;
    sessionPhase: 'opening' | 'middle' | 'closing';
  };
}

export interface MultiTherapistConversation {
  id: string;
  status: 'active' | 'completed' | 'paused';
  startTime: string;
  endTime?: string;
  patientMessages: Message[];
  therapistConversations: {
    cbtOnly: Message[];
    miFixedPretreatment: Message[];
    dynamicAdaptive: Message[];
  };
  therapistThoughts: {
    cbtOnly: AgentThought[];
    miFixedPretreatment: AgentThought[];
    dynamicAdaptive: AgentThought[];
  };
  patientThoughts: AgentThought[];
  studyMetadata: {
    patientPersonaId: string;
    studyConfiguration: any;
  };
}

export interface ComparativeCBTEvaluationResult {
  id: string;
  conversationId: string;
  evaluationTimestamp: string;
  evaluations: {
    cbtOnly: CBTEvaluationResult;
    miFixedPretreatment: CBTEvaluationResult;
    dynamicAdaptive: CBTEvaluationResult;
  };
  comparison: {
    bestPerforming: TherapistPersonaType;
    scoreComparison: {
      cbtOnly: number;
      miFixedPretreatment: number;
      dynamicAdaptive: number;
    };
    insights: string[];
    recommendations: string[];
  };
}

export interface WebSocketMessage {
  type: 'welcome' | 'message' | 'thought' | 'error' | 'conversation_update' | 'agent_response' |
        'multi_conversation_created' | 'multi_conversation_started' | 'multi_therapist_message' |
        'patient_message' | 'differentiated_patient_messages' | 'multi_conversation_ended' | 'comparative_evaluation_complete';
  data?: any;
  timestamp: string;
  conversationId?: string;
}

export interface AgentResponse {
  message: string;
  thought: string;
  metadata: MessageMetadata;
  processingTime: number;
}

export interface ConnectionStatus {
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  lastConnected?: string;
  reconnectAttempts?: number;
}
